import React, { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import toast from 'react-hot-toast';
import Loader from 'components/Loader';

export default function EndMockInterviewSection() {
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    const cleanup = async () => {
      try {
        // Clear specific localStorage items
        localStorage.removeItem('interviewVideo');
        localStorage.removeItem('questions');
        localStorage.removeItem('interviewData');
        localStorage.removeItem(`ReviewVideoProfile${id}`);

        // Show success message
        toast.success('Mock interview submitted successfully');

        // Add a small delay before navigation to ensure cleanup is complete
        setTimeout(() => {
          navigate("/candidate/dashboard");
        }, 1500);
      } catch (error) {
        console.error('Error during cleanup:', error);
        toast.error('Error during cleanup. Please try again.');
        navigate("/candidate/dashboard");
      }
    };

    cleanup();
  }, [navigate, id]);

  return (
    <div className="flex h-screen items-center justify-center">
      <Loader text="Finalizing your mock interview..." />
    </div>
  );
}