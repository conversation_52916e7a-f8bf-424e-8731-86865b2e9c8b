import React, { useEffect, useState } from "react";
import DeviceTestingSection from "./components/DeviceTestingSection";
import TermsAndConditionsSection from "./components/TermsAndConditionsSection";
import InterviewSection from "./components/InterviewSection";
import EndInterviewSection from "./components/EndInterviewSection";
import { useMediaStreams } from "./hooks/useMediaStreams";
import { useRecording } from "./hooks/useRecording";
import { useQuestions } from "./hooks/useQuestions";
import { useInterviewState } from "./hooks/useInterviewState";
import Loader from "components/Loader";
import { useNavigate, useParams } from "react-router-dom";
import ReviewVideoProfile from "./components/ReviewVideoProfile";
import toast from "react-hot-toast";

export default function DeviceTest() {
  const navigate = useNavigate();
  const [currentSection, setCurrentSection] = useState("loading");
  const [isLoading, setIsLoading] = useState(false);
  const [loadingText, setLoadingText] = useState("Loading...");
  const { vpid } = useParams();
  const { localCamStream, startWebcam, stopAllStreams } = useMediaStreams();
  const { startRecording, stopRecording, recordedChunks } =
    useRecording(localCamStream);
  const {
    isInterviewActive,
    startInterview,
    endInterview,
    resetInterview
  } = useInterviewState();

  const {
    questions,
    currentQuestion,
    currentIndex,
    nextQuestion,
    startAnswering,
    updateAnswer,
    initializeQuestions,
    loadQuestionsData,
    activateQuestions,
    questionsLoaded,
    error,
    isLoading: questionsLoading,
  } = useQuestions(isInterviewActive);
  const [score, setScore] = useState({
    score: {
      Communication_Score: 0,
      Skill_Score: 0,
      Overall_Score: 0,
    },
    Suggestions: "",
  });
  const [videoUrl, setVideoUrl] = useState("");
  const [status, setStatus] = useState("inactive"); // Default to inactive until score is determined

  const handleStartInterview = () => {
    if (localCamStream) {
      setCurrentSection("termsAndConditions");
    } else {
      toast.error("Please allow access to your webcam and microphone");
    }
  };

  const handleFullScreen = async () => {
    try {
      const element = document.documentElement;
      if (element.requestFullscreen) {
        await element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        await element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        await element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        await element.msRequestFullscreen();
      }
    } catch (error) {
      console.warn('Failed to enter fullscreen mode:', error);
      // Continue anyway as this is not critical

    }
  };

  const handleAcceptTerms = async () => {
    try {
      console.log('🎬 User accepted terms - starting interview');
      console.log('Current state:', {
        questionsLoaded,
        questionsCount: questions?.length || 0,
        currentQuestion: currentQuestion?.question || 'none'
      });

      // Check if questions are ready, if not try to load them
      if (!questionsLoaded || !questions || questions.length === 0) {
        console.log('Questions not loaded, attempting to load from cookies...');
        try {
          await loadQuestionsData();
          // Wait a moment for state to update
          await new Promise(resolve => setTimeout(resolve, 500));

          if (!questionsLoaded || !questions || questions.length === 0) {
            console.error('Still no questions after loading attempt');
            toast.error("Failed to load interview questions. Please refresh the page.");
            return;
          }
        } catch (loadError) {
          console.error('Failed to load questions:', loadError);
          toast.error("Failed to load interview questions. Please refresh the page.");
          return;
        }
      }

      // Activate questions first
      const activated = activateQuestions();
      if (!activated) {
        console.error('Failed to activate questions');
        toast.error("Failed to start interview - questions not ready");
        return;
      }

      // Start the interview state
      startInterview();

      // Start recording and enter fullscreen
      setCurrentSection("interview");
      startRecording();
      handleFullScreen();
    } catch (error) {
      console.error('Error starting interview:', error);
      toast.error(`Failed to start interview: ${error.message}`);
    }
  };

  const exitFullScreen = async () => {
    try {
      const isFullScreen =
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement;

      if (!isFullScreen) {
        console.log("Not in fullscreen mode");
        return;
      }

      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        await document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        await document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        await document.msExitFullscreen();
      }
    } catch (error) {
      console.warn('Failed to exit fullscreen mode:', error);
      // Continue anyway as this is not critical
    }
  };

  // Function to download recorded video
  // const handleDownload = () => {
  //   console.log(recordedChunks)
  //   if (recordedChunks.length > 0) {
  //     const blob = new Blob(recordedChunks, { type: "video/webm" });
  //     const url = URL.createObjectURL(blob);
  //     const a = document.createElement("a");
  //     document.body.appendChild(a);
  //     a.style = "display: none";
  //     a.href = url;
  //     a.download = "interview-recording.webm"; // You can customize the file name
  //     a.click();
  //     window.URL.revokeObjectURL(url);
  //   }
  // };

  const handleEndInterview = async () => {
    let scoreData = null;
    let uploadSuccess = false;

    try {
      setLoadingText("Preparing interview data...");
      setIsLoading(true);
      setCurrentSection("loading");

      // Stop all active processes
      window.speechSynthesis.cancel();
      
      // Stop recording and wait for completion
      await stopRecording();
      
      // Wait a bit for final chunks to be processed
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check if we have video data
      if (!recordedChunks || recordedChunks.length === 0) {
        throw new Error("No video data was recorded");
      }
      
      stopAllStreams();
      exitFullScreen();

      // Get questions state from localStorage
      
      const questionsWithTimestamps = JSON.parse(localStorage.getItem("questions") || "[]");
      
      // Validate we have question data
      if (!questionsWithTimestamps.length) {
        toast.error("No interview data found. Please restart the interview.");
        setCurrentSection("deviceTesting");
        return;
      }

      console.log("Preparing video for upload");
      const file = new Blob(recordedChunks, {
        type: "video/webm;codecs=vp8,opus"
      });

      if (file.size === 0) {
        throw new Error("Generated video file is empty");
      }

      console.log("Video blob created:", {
        size: file.size,
        type: file.type,
        chunks: recordedChunks.length
      });
      setLoadingText("Uploading Video In Bucket");

      // Attempt file upload with retries
      const maxRetries = 3;
      let lastError = null;

      for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
          // Get a fresh pre-signed URL for each attempt
          console.log(
            `Getting pre-signed URL (attempt ${attempt + 1}/${maxRetries})`
          );
          const host = import.meta.env.VITE_APP_HOST || 'https://api.zoomjobs.in';
          console.log('Using API host:', host);

          const urlResponse = await fetch(
            `${host}/api/v1/get-s3-url/${encodeURIComponent(vpid)}?contentType=${encodeURIComponent(file.type)}`, {
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
              },
              credentials: 'include'
            }
          );

          if (!urlResponse.ok) {
            const errorText = await urlResponse.text();
            throw new Error(
              `Failed to get upload URL: ${urlResponse.status} ${urlResponse.statusText}. Details: ${errorText}`
            );
          }

          const { url } = await urlResponse.json();
          console.log(`Got pre-signed URL (attempt ${attempt + 1})`);

          // Attempt the upload with the fresh URL
          console.log(`Uploading file (attempt ${attempt + 1}/${maxRetries})`);

          // Parse the pre-signed URL to get the base URL and query parameters
          const urlObj = new URL(url);
          const contentType = file.type;

          const uploadResponse = await fetch(url, {
            method: "PUT",
            body: file,
            headers: {
              "Content-Type": contentType,
              "Content-Length": file.size.toString(),
            },
            // Increase timeout for large files
            timeout: 300000, // 5 minutes
          });

          // Get response body even for errors - S3 sends error details in the body
          const s3ResponseText = await uploadResponse.text();

          if (!uploadResponse.ok) {
            // Parse XML error response
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(s3ResponseText, "text/xml");
            const errorCode = xmlDoc.querySelector("Code")?.textContent;
            const errorMessage = xmlDoc.querySelector("Message")?.textContent;

            throw new Error(
              `Upload failed (${errorCode}): ${errorMessage || responseText}`
            );
          }

          // If we get here, upload was successful
          console.log(`Upload successful on attempt ${attempt + 1}`);
          const finalUrl = url.split("?")[0];
          setVideoUrl(finalUrl);
          console.log("Video URL set to:", finalUrl);
          uploadSuccess = true;

          // Generate Score Using LLM after successful upload
          console.log("Generating Score Now");
          setLoadingText("Getting Score from LLM");

          const Get_Score_Url = `${
            import.meta.env.VITE_APP_HOST
          }/api/v1/generateScore`;

          // Generate score
          let scoreResponse;
          try {
            scoreResponse = await fetch(Get_Score_Url, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                InterviewObject: questionsWithTimestamps.map((q) => ({
                  ...q,
                  answer: q.answer || "No answer provided device test",
                })),
              }),
            });

            if (!scoreResponse.ok) {
              throw new Error(
                `Score generation failed: ${scoreResponse.status} ${scoreResponse.statusText}`
              );
            }

            scoreData = await scoreResponse.json();
            console.log("Raw score response:", scoreData);

            // Always set the score data even if it's default/invalid
            setScore(scoreData);

            if (scoreData.Suggestions?.includes("default score")) {
              console.warn("Using default score:", scoreData);
              toast("Using default scoring due to processing limitations", {
                icon: "⚠️",
              });
            } else if (
              !scoreData.score ||
              !scoreData.score.Skill_Score ||
              !scoreData.score.Communication_Score ||
              !scoreData.score.Overall_Score
            ) {
              console.warn(
                "Received potentially invalid score format:",
                scoreData
              );
              toast("Score format may be incorrect - using available data", {
                icon: "⚠️",
              });
            } else {
              console.log("Score processing completed successfully");
            }
          } catch (scoreError) {
            console.error("Score generation error:", scoreError);
            toast("Failed to generate score. Using default values.", {
              icon: "⚠️",
            });
            scoreData = {
              score: {
                Skill_Score: 5,
                Communication_Score: 5,
                Overall_Score: 5,
              },
              Suggestions: "Score generation failed. This is a default score.",
            };
            setScore(scoreData);
          }

          // Validate and prepare data for API submission
          if (!finalUrl || !scoreData || !questions || !vpid) {
            throw new Error(
              "Missing required data. Need video URL, score, questions and profile ID."
            );
          }

          // Save to localStorage
          const storageData = {
            videoUrl: finalUrl,
            score: scoreData,
            status: scoreData.score.Overall_Score > 5 ? "active" : "inactive", // Score-based status
          };

          console.log("Saving to localStorage:", storageData);
          localStorage.setItem(
            `ReviewVideoProfile${vpid}`,
            JSON.stringify(storageData)
          );

          // Submit to API
          setLoadingText("Updating Video Profile Data...");
          const formData = {
            videoProfileId: vpid,
            videoUrl: finalUrl,
            score: scoreData,
            status: scoreData.score.Overall_Score > 5 ? "active" : "inactive", // Score-based status
            questions: questionsWithTimestamps.map((q) => ({
              ...q,
              answer: q.answer || "No answer provided device test",
            })),
          };

          // Define the API URL
          const Finish_videoprofile_url = `${
            import.meta.env.VITE_APP_HOST
          }/api/v1/add-video-resume`;

          // Make an API call using fetch
          const response = await fetch(Finish_videoprofile_url, {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(formData),
          });

          // Check if the response is okay
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(
              `HTTP error! status: ${response.status}. Message: ${
                errorData.message || "Unknown error"
              }`
            );
          }

          // Parse the JSON response
          const Apidata = await response.json();
          console.log("Success:", Apidata);
          localStorage.removeItem("formData");
          setCurrentSection("reviewVideoProfile");
          setIsLoading(false);
          break;
        } catch (uploadError) {
          lastError = uploadError;
          console.error(`Attempt ${attempt + 1} failed:`, uploadError.message);

          // Check if this was a score generation error
          if (uploadError.message.includes("Score generation failed")) {
            // Don't retry for score generation errors, just use the default score
            console.log("Using default score due to generation error");
            break;
          }

          if (attempt === maxRetries - 1) {
            // Only show error on final attempt
            toast(`Upload failed after ${maxRetries} attempts`, {
              icon: "❌",
            });

            throw new Error(
              `File upload failed after ${maxRetries} attempts. Last error: ${uploadError.message}`
            );
          }

          // Show retry message and wait before next attempt
          toast(`Retrying upload (attempt ${attempt + 2}/${maxRetries})`, {
            icon: "⏳",
          });
          await new Promise((resolve) =>
            setTimeout(resolve, Math.pow(2, attempt) * 1000)
          );
        }
      }
    } catch (error) {
      setIsLoading(false);
      const errorMessage = error.message || "An unknown error occurred";
      console.error("Error during interview ending:", errorMessage);

      // Handle different error scenarios
      if (
        error.message.includes("Failed to fetch") ||
        error.message.includes("Network")
      ) {
        // Network error
        toast("Network error - please check your connection", {
          icon: "🌐",
        });
      } else if (
        error.message.includes("score") ||
        error.message.includes("Score")
      ) {
        // Score generation error with uploaded video
        if (uploadSuccess) {
          toast("Using default scoring - processing limitations", {
            icon: "⚠️",
          });
          scoreData = {
            score: {
              Skill_Score: 5,
              Communication_Score: 5,
              Overall_Score: 5,
            },
            Suggestions: "Score generation failed. Using default scoring.",
          };
          setScore(scoreData);
          setCurrentSection("reviewVideoProfile");
          return;
        }
      } else {
        // Other errors
        toast("Interview processing encountered an error", {
          icon: "❌",
        });
      }

      // Final fallback handling
      if (uploadSuccess) {
        if (scoreData?.score) {
          console.log("Continuing with available score data despite error");
          setCurrentSection("reviewVideoProfile");
        } else {
          console.log("No score data available - using default");
          const defaultScore = {
            score: {
              Skill_Score: 5,
              Communication_Score: 5,
              Overall_Score: 5,
            },
            Suggestions: "Score unavailable. Using default values.",
          };
          setScore(defaultScore);
          setCurrentSection("reviewVideoProfile");
        }
      } else {
        console.log("Critical error - resetting to device testing");
        setCurrentSection("deviceTesting");
      }
    }
  };

  const finishVideoProfile = async () => {
    try {
      let scores = 0;

      if (score?.score?.Overall_Score !== undefined) {
        scores = score.score.Overall_Score;
      } else {
        console.warn("Using default score of 0 - score data may be incomplete");
        toast.warning("Using default overall score due to incomplete data");
      }

      const formData = {
        videoProfileId: vpid,
        score: scores,
        isDefaultScore: score?.Suggestions?.includes("default score") || false,
      };

      // Define the API URL
      const Finish_videoprofile_url = `${
        import.meta.env.VITE_APP_HOST
      }/api/v1/finish-video-resume`;

      // Make an API call using fetch
      const response = await fetch(Finish_videoprofile_url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json", // To Pass JSON if formData is a plain object
        },
        body: JSON.stringify(formData), // Send formData as JSON
      });
      const data = await response.json();
      console.log(data);
      setCurrentSection("endInterview");
      localStorage.removeItem(`questions`);
      localStorage.removeItem(`ReviewVideoProfile${vpid}`);
    } catch (error) {
      const errorMessage = error.message || "An unknown error occurred";
      console.error("Error during profile completion:", errorMessage);
      toast.error(`Failed to complete profile: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchData = async () => {
    setIsLoading(true);
    try {
      // Try to get data from localStorage first
      const localDataStr = localStorage.getItem(`ReviewVideoProfile${vpid}`);
      if (localDataStr) {
        try {
          const localData = JSON.parse(localDataStr);
          if (localData?.score?.score && typeof localData.score.score === 'object') {
            setScore(localData.score);
            setVideoUrl(localData.videoUrl);
            setStatus(localData.status);
            setCurrentSection("reviewVideoProfile");
            return;
          }
        } catch (e) {
          console.error('Error parsing localStorage data:', e);
        }
      }

      // If no valid localStorage data, fetch from API
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume-data/${vpid}`,
        {
          method: "GET",
        }
      );

      const res = await response.json();
      
      if (!response.ok) {
        throw new Error(res.message || "Failed to fetch video resume data");
      }

      if (!res.data) {
        throw new Error("No data received from server");
      }

      if (res.data.score && res.data.video_url) {
        try {
          const parsedScore = JSON.parse(res.data.score);
          if (!parsedScore.score || typeof parsedScore.score !== 'object') {
            throw new Error('Invalid score format in API response');
          }
          setScore(parsedScore);
          setVideoUrl(res.data.video_url);
          setStatus(res.data.status);
          setCurrentSection("reviewVideoProfile");
        } catch (error) {
          console.error('Error parsing score from API:', error);
          setScore({
            score: {
              Communication_Score: 0,
              Skill_Score: 0,
              Overall_Score: 0,
            },
            Suggestions: "Score data unavailable or invalid",
          });
          setVideoUrl(res.data.video_url);
          setStatus(res.data.status);
          setCurrentSection("reviewVideoProfile");
        }
      } else if (res.data.questions) {
        try {
          const parsedQuestions = JSON.parse(res.data.questions);
          console.log("Parsed API response:", {
            rawQuestions: res.data.questions,
            parsedQuestions: parsedQuestions
          });

          if (!Array.isArray(parsedQuestions) || parsedQuestions.length === 0) {
            throw new Error("No valid questions found");
          }

          console.log("About to initialize with questions:", parsedQuestions);
          const success = await initializeQuestions(parsedQuestions);
          console.log("Initialization result:", {
            success,
            currentQuestions: questions,
            currentIndex
          });

          if (!success) {
            throw new Error("Failed to initialize questions");
          }

          console.log("Questions initialized successfully:", {
            count: parsedQuestions.length,
            firstQuestion: parsedQuestions[0]?.question
          });

          setCurrentSection("deviceTesting");
        } catch (parseError) {
          console.error("Error initializing questions:", parseError);
          throw new Error("Failed to setup interview questions");
        }
      } else {
        throw new Error("Invalid data format received from server");
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error(error.message || "Something went wrong");
      navigate("/");
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    fetchData();
  }, []);

  // Load questions when entering device testing mode if not already loaded
  useEffect(() => {
    if (currentSection === "deviceTesting" && !questionsLoaded && !isLoading) {
      console.log('Device testing section loaded but no questions - attempting to load from cookies');
      loadQuestionsData().catch(error => {
        console.error('Failed to load questions in device testing:', error);
        // Don't show error toast here as fetchData might have already handled it
      });
    }
  }, [currentSection, questionsLoaded, isLoading]);

  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (currentSection === "interview") {
        event.preventDefault();
        event.returnValue =
          "You can't refresh while taking Interview. Otherwise you may loose your progress"; // Some browsers require this for custom messages
        return "You can't refresh while taking Interview. Otherwise you may loose your progress"; // This is necessary for most modern browsers
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    // Cleanup event listener when the component unmounts or interview ends
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [currentSection]);

  return (
    <div className="min-w-screen flex min-h-screen items-center justify-center bg-[#F4F7FE] text-gray-900">
      {/* Debug Info - Remove in production */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed top-2 right-2 z-50 bg-black bg-opacity-75 text-white p-2 rounded text-xs">
          <div>Section: {currentSection}</div>
          <div>Questions Loaded: {questionsLoaded ? 'Yes' : 'No'}</div>
          <div>Questions Count: {questions?.length || 0}</div>
          <div>Interview Active: {isInterviewActive ? 'Yes' : 'No'}</div>
          <div>Current Question: {currentQuestion?.question ? 'Set' : 'None'}</div>
        </div>
      )}

      {/* Loader */}
      {(isLoading || currentSection === "loading") && <Loader text={loadingText} />}
      {currentSection === "deviceTesting" && (
        <DeviceTestingSection
          localCamStream={localCamStream}
          startWebcam={startWebcam}
          onStartInterview={handleStartInterview}
        />
      )}
      {currentSection === "termsAndConditions" && (
        <TermsAndConditionsSection onAccept={handleAcceptTerms} />
      )}
      {currentSection === "interview" && (
        <>
          {(!questions || questions.length === 0) ? (
            <div className="flex h-screen items-center justify-center">
              <div className="max-w-md rounded-lg bg-blue-50 p-6 text-center">
                <div className="mb-4">
                  <div className="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-blue-200 border-t-blue-600"></div>
                </div>
                <h2 className="mb-4 text-xl font-semibold text-blue-700">
                  Loading Questions
                </h2>
              </div>
            </div>
          ) : (
            <InterviewSection
              localCamStream={localCamStream}
              questions={questions}
              currentQuestion={currentQuestion}
              currentIndex={currentIndex}
              error={error}
              isLoading={questionsLoading}
              updateAnswer={updateAnswer}
              isInterviewActive={isInterviewActive}
              onNextQuestion={async () => {
                try {
                  const MIN_QUESTIONS = 5;
                  if (currentIndex + 1 < MIN_QUESTIONS) {
                    await nextQuestion();
                    return true;
                  } else {
                    if (window.confirm('Would you like to answer another question? Click Cancel to finish the interview.')) {
                      await nextQuestion();
                      return true;
                    } else {
                      handleEndInterview();
                      return false;
                    }
                  }
                } catch (error) {
                  console.error("Error getting next question:", error);
                  toast.error("Failed to get next question. Please try again.");
                  return false;
                }
              }}
             onEndInterview={handleEndInterview}
             startAnswering={startAnswering}
           />
         )}
       </>
     )}
      {currentSection === "reviewVideoProfile" && (
        <ReviewVideoProfile
          status={status}
          finishVideoProfile={finishVideoProfile}
          videoUrl={videoUrl}
          score={score}
        />
      )}
      {currentSection === "endInterview" && <EndInterviewSection />}
    </div>
  );
}
