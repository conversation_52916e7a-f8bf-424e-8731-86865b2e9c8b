import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import ReviewMockInterview from './components/ReviewMockInterview';
import toast from 'react-hot-toast';

export default function MockReview() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [interviewData, setInterviewData] = useState(null);

  const loadStoredData = useCallback(() => {
    const stored = localStorage.getItem('interviewData');
    if (!stored) return null;

    try {
      const parsed = JSON.parse(stored);
      if (parsed.mock_interview_id === id) {
        console.log('Found matching interview data in localStorage:', parsed);
        return parsed;
      }
    } catch (err) {
      console.error('Error parsing stored interview data:', err);
    }
    return null;
  }, [id]);

  const transformData = useCallback((apiResponse = null, storedData = null) => {
    console.log('Transforming data:', { apiResponse, storedData });
    const apiData = apiResponse?.data; // Extract data from response structure
    const transformed = {
      videoUrl: apiData?.video_url || null,
      status: apiData?.status || storedData?.status || 'inactive',
      score: {
        score: {
          Communication_Score: 0,
          Skill_Score: 0,
          Overall_Score: 0
        },
        Suggestions: '',
        evaluation: []
      }
    };

    // Add evaluation from stored questions if available
    if (storedData?.questions) {
      transformed.score.evaluation = storedData.questions.map(q => ({
        Question: q.question,
        Your_Answer: q.answer || null,
        Expected_Answer: q.expected_answer || null
      }));
    }

    // Override with API score data if available
    if (apiData?.score) {
      try {
        const apiScore = typeof apiData.score === 'string' ? JSON.parse(apiData.score) : apiData.score;
        transformed.score = {
          ...apiScore,
          evaluation: transformed.score.evaluation
        };
      } catch (err) {
        console.error('Error parsing API score:', err);
      }
    }

    return transformed;
  }, []);

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        setIsLoading(true);
        
        // Ensure ID is handled as a string to preserve large numbers
        const interviewId = id?.toString();
        console.log('Fetching mock interview data for ID:', {
          id: interviewId,
          type: typeof interviewId,
          original: id
        });
        
        // First load data from localStorage
        const storedData = loadStoredData();
        if (storedData && isMounted) {
          console.log('Using stored data:', storedData);
          setInterviewData(transformData(null, storedData));
        }

        // Then try to fetch from API
        const endpoint = `${import.meta.env.VITE_APP_HOST}/api/v1/mock-resume-data/${interviewId}`;
        console.log('Making API request to:', endpoint);
        const response = await fetch(endpoint,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );

        const data = await response.json();
        console.log('API response status:', response.status);
        console.log('API response data:', data);
        
        if (!response.ok) {
          console.error('API error:', {
            status: response.status,
            statusText: response.statusText,
            data: data
          });
          throw new Error(data.message || 'Failed to fetch interview data');
        }

        if (data && isMounted) {
          const transformed = transformData(data, storedData);
          console.log('Transformed data:', transformed);
          setInterviewData(transformed);
        }

      } catch (error) {
        console.error('Error fetching/transforming data:', {
          error,
          id,
          url: `${import.meta.env.VITE_APP_HOST}/api/v1/mock-resume-data/${id}`
        });
        if (isMounted) {
          toast.error(error.message || 'Failed to load interview data');
          navigate('/candidate/mock-interviews');
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchData();
    return () => { isMounted = false; };
  }, [id, navigate, loadStoredData, transformData]);

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      // Ensure ID is handled consistently as a string
      const interviewId = id?.toString();
      console.log('Submitting mock interview:', {
        id: interviewId,
        type: typeof interviewId,
        original: id
      });
      const endpoint = `${import.meta.env.VITE_APP_HOST}/api/v1/mock-resume-data/${interviewId}/submit`;
      const response = await fetch(endpoint,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to submit interview');
      }

      const data = await response.json();
      console.log('Submit response:', data);

      toast.success('Interview submitted successfully');
      navigate(`/candidate/mock-end/${id}`);
    } catch (error) {
      console.error('Error:', error);
      toast.error('Failed to submit interview');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-blue-500 border-t-transparent"></div>
          <p className="mt-2">Loading interview data...</p>
        </div>
      </div>
    );
  }

  if (!interviewData) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <p className="text-red-500">Failed to load interview data. Please try again.</p>
          <button
            onClick={() => navigate('/candidate/mock-interviews')}
            className="mt-4 rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
          >
            Back to Mock Interviews
          </button>
        </div>
      </div>
    );
  }


  return (
    <ReviewMockInterview
      videoUrl={interviewData.videoUrl}
      score={interviewData.score}
      status={interviewData.status}
      finishVideoProfile={handleSubmit}
      isSubmitting={isSubmitting}
    />
  );
}