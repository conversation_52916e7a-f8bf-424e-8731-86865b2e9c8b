import {
  AwsSdkSigV4Signer,
  Client,
  Command,
  DEFAULT_MAX_ATTEMPTS,
  DEFAULT_RETRY_MODE,
  DEFAULT_USE_DUALSTACK_ENDPOINT,
  DEFAULT_USE_FIPS_ENDPOINT,
  DefaultIdentityProviderConfig,
  EndpointCache,
  FetchHttpHandler,
  NoOpLogger,
  SENSITIVE_STRING,
  ServiceException,
  Sha256,
  _json,
  awsEndpointFunctions,
  calculateBodyLength,
  collectBody,
  createAggregatedClient,
  createDefaultUserAgentProvider,
  createPaginator,
  customEndpointFunctions,
  decorateServiceException,
  expectInt32,
  expectNonNull,
  expectNumber,
  expectObject,
  expectString,
  fromBase64,
  fromUtf8,
  getAwsRegionExtensionConfiguration,
  getContentLengthPlugin,
  getDefaultExtensionConfiguration,
  getEndpointPlugin,
  getHostHeaderPlugin,
  getHttpAuthSchemeEndpointRuleSetPlugin,
  getHttpHandlerExtensionConfiguration,
  getHttpSigningPlugin,
  getLoggerPlugin,
  getRecursionDetectionPlugin,
  getRetryPlugin,
  getSerdePlugin,
  getSmithyContext,
  getUserAgentPlugin,
  invalidProvider,
  loadConfigsForDefaultMode,
  loadRestJsonErrorCode,
  map,
  normalizeProvider,
  parseEpochTimestamp,
  parseJsonBody,
  parseJsonErrorBody,
  parseUrl,
  requestBuilder,
  resolveAwsRegionExtensionConfiguration,
  resolveAwsSdkSigV4Config,
  resolveDefaultRuntimeConfig,
  resolveDefaultsModeConfig,
  resolveEndpoint,
  resolveEndpointConfig,
  resolveHostHeaderConfig,
  resolveHttpHandlerRuntimeConfig,
  resolveRegionConfig,
  resolveRetryConfig,
  resolveUserAgentConfig,
  sdkStreamMixin,
  streamCollector,
  strictParseInt32,
  take,
  toBase64,
  toUtf8,
  withBaseException
} from "./chunk-W3SGTAH5.js";
import "./chunk-V5JBZRLT.js";
import "./chunk-KSSH4POC.js";
import "./chunk-ROME4SDB.js";

// node_modules/@aws-sdk/client-polly/dist-es/auth/httpAuthSchemeProvider.js
var defaultPollyHttpAuthSchemeParametersProvider = async (config, context, input) => {
  return {
    operation: getSmithyContext(context).operation,
    region: await normalizeProvider(config.region)() || (() => {
      throw new Error("expected `region` to be configured for `aws.auth#sigv4`");
    })()
  };
};
function createAwsAuthSigv4HttpAuthOption(authParameters) {
  return {
    schemeId: "aws.auth#sigv4",
    signingProperties: {
      name: "polly",
      region: authParameters.region
    },
    propertiesExtractor: (config, context) => ({
      signingProperties: {
        config,
        context
      }
    })
  };
}
var defaultPollyHttpAuthSchemeProvider = (authParameters) => {
  const options = [];
  switch (authParameters.operation) {
    default: {
      options.push(createAwsAuthSigv4HttpAuthOption(authParameters));
    }
  }
  return options;
};
var resolveHttpAuthSchemeConfig = (config) => {
  const config_0 = resolveAwsSdkSigV4Config(config);
  return {
    ...config_0
  };
};

// node_modules/@aws-sdk/client-polly/dist-es/endpoint/EndpointParameters.js
var resolveClientEndpointParameters = (options) => {
  return {
    ...options,
    useDualstackEndpoint: options.useDualstackEndpoint ?? false,
    useFipsEndpoint: options.useFipsEndpoint ?? false,
    defaultSigningName: "polly"
  };
};
var commonParams = {
  UseFIPS: { type: "builtInParams", name: "useFipsEndpoint" },
  Endpoint: { type: "builtInParams", name: "endpoint" },
  Region: { type: "builtInParams", name: "region" },
  UseDualStack: { type: "builtInParams", name: "useDualstackEndpoint" }
};

// node_modules/@aws-sdk/client-polly/package.json
var package_default = {
  name: "@aws-sdk/client-polly",
  description: "AWS SDK for JavaScript Polly Client for Node.js, Browser and React Native",
  version: "3.699.0",
  scripts: {
    build: "concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'",
    "build:cjs": "node ../../scripts/compilation/inline client-polly",
    "build:es": "tsc -p tsconfig.es.json",
    "build:include:deps": "lerna run --scope $npm_package_name --include-dependencies build",
    "build:types": "tsc -p tsconfig.types.json",
    "build:types:downlevel": "downlevel-dts dist-types dist-types/ts3.4",
    clean: "rimraf ./dist-* && rimraf *.tsbuildinfo",
    "extract:docs": "api-extractor run --local",
    "generate:client": "node ../../scripts/generate-clients/single-service --solo polly"
  },
  main: "./dist-cjs/index.js",
  types: "./dist-types/index.d.ts",
  module: "./dist-es/index.js",
  sideEffects: false,
  dependencies: {
    "@aws-crypto/sha256-browser": "5.2.0",
    "@aws-crypto/sha256-js": "5.2.0",
    "@aws-sdk/client-sso-oidc": "3.699.0",
    "@aws-sdk/client-sts": "3.699.0",
    "@aws-sdk/core": "3.696.0",
    "@aws-sdk/credential-provider-node": "3.699.0",
    "@aws-sdk/middleware-host-header": "3.696.0",
    "@aws-sdk/middleware-logger": "3.696.0",
    "@aws-sdk/middleware-recursion-detection": "3.696.0",
    "@aws-sdk/middleware-user-agent": "3.696.0",
    "@aws-sdk/region-config-resolver": "3.696.0",
    "@aws-sdk/types": "3.696.0",
    "@aws-sdk/util-endpoints": "3.696.0",
    "@aws-sdk/util-user-agent-browser": "3.696.0",
    "@aws-sdk/util-user-agent-node": "3.696.0",
    "@smithy/config-resolver": "^3.0.12",
    "@smithy/core": "^2.5.3",
    "@smithy/fetch-http-handler": "^4.1.1",
    "@smithy/hash-node": "^3.0.10",
    "@smithy/invalid-dependency": "^3.0.10",
    "@smithy/middleware-content-length": "^3.0.12",
    "@smithy/middleware-endpoint": "^3.2.3",
    "@smithy/middleware-retry": "^3.0.27",
    "@smithy/middleware-serde": "^3.0.10",
    "@smithy/middleware-stack": "^3.0.10",
    "@smithy/node-config-provider": "^3.1.11",
    "@smithy/node-http-handler": "^3.3.1",
    "@smithy/protocol-http": "^4.1.7",
    "@smithy/smithy-client": "^3.4.4",
    "@smithy/types": "^3.7.1",
    "@smithy/url-parser": "^3.0.10",
    "@smithy/util-base64": "^3.0.0",
    "@smithy/util-body-length-browser": "^3.0.0",
    "@smithy/util-body-length-node": "^3.0.0",
    "@smithy/util-defaults-mode-browser": "^3.0.27",
    "@smithy/util-defaults-mode-node": "^3.0.27",
    "@smithy/util-endpoints": "^2.1.6",
    "@smithy/util-middleware": "^3.0.10",
    "@smithy/util-retry": "^3.0.10",
    "@smithy/util-stream": "^3.3.1",
    "@smithy/util-utf8": "^3.0.0",
    tslib: "^2.6.2"
  },
  devDependencies: {
    "@tsconfig/node16": "16.1.3",
    "@types/node": "^16.18.96",
    concurrently: "7.0.0",
    "downlevel-dts": "0.10.1",
    rimraf: "3.0.2",
    typescript: "~4.9.5"
  },
  engines: {
    node: ">=16.0.0"
  },
  typesVersions: {
    "<4.0": {
      "dist-types/*": [
        "dist-types/ts3.4/*"
      ]
    }
  },
  files: [
    "dist-*/**"
  ],
  author: {
    name: "AWS SDK for JavaScript Team",
    url: "https://aws.amazon.com/javascript/"
  },
  license: "Apache-2.0",
  browser: {
    "./dist-es/runtimeConfig": "./dist-es/runtimeConfig.browser"
  },
  "react-native": {
    "./dist-es/runtimeConfig": "./dist-es/runtimeConfig.native"
  },
  homepage: "https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-polly",
  repository: {
    type: "git",
    url: "https://github.com/aws/aws-sdk-js-v3.git",
    directory: "clients/client-polly"
  }
};

// node_modules/@aws-sdk/client-polly/dist-es/endpoint/ruleset.js
var s = "required";
var t = "fn";
var u = "argv";
var v = "ref";
var a = true;
var b = "isSet";
var c = "booleanEquals";
var d = "error";
var e = "endpoint";
var f = "tree";
var g = "PartitionResult";
var h = { [s]: false, "type": "String" };
var i = { [s]: true, "default": false, "type": "Boolean" };
var j = { [v]: "Endpoint" };
var k = { [t]: c, [u]: [{ [v]: "UseFIPS" }, true] };
var l = { [t]: c, [u]: [{ [v]: "UseDualStack" }, true] };
var m = {};
var n = { [t]: "getAttr", [u]: [{ [v]: g }, "supportsFIPS"] };
var o = { [t]: c, [u]: [true, { [t]: "getAttr", [u]: [{ [v]: g }, "supportsDualStack"] }] };
var p = [k];
var q = [l];
var r = [{ [v]: "Region" }];
var _data = { version: "1.0", parameters: { Region: h, UseDualStack: i, UseFIPS: i, Endpoint: h }, rules: [{ conditions: [{ [t]: b, [u]: [j] }], rules: [{ conditions: p, error: "Invalid Configuration: FIPS and custom endpoint are not supported", type: d }, { conditions: q, error: "Invalid Configuration: Dualstack and custom endpoint are not supported", type: d }, { endpoint: { url: j, properties: m, headers: m }, type: e }], type: f }, { conditions: [{ [t]: b, [u]: r }], rules: [{ conditions: [{ [t]: "aws.partition", [u]: r, assign: g }], rules: [{ conditions: [k, l], rules: [{ conditions: [{ [t]: c, [u]: [a, n] }, o], rules: [{ endpoint: { url: "https://polly-fips.{Region}.{PartitionResult#dualStackDnsSuffix}", properties: m, headers: m }, type: e }], type: f }, { error: "FIPS and DualStack are enabled, but this partition does not support one or both", type: d }], type: f }, { conditions: p, rules: [{ conditions: [{ [t]: c, [u]: [n, a] }], rules: [{ endpoint: { url: "https://polly-fips.{Region}.{PartitionResult#dnsSuffix}", properties: m, headers: m }, type: e }], type: f }, { error: "FIPS is enabled but this partition does not support FIPS", type: d }], type: f }, { conditions: q, rules: [{ conditions: [o], rules: [{ endpoint: { url: "https://polly.{Region}.{PartitionResult#dualStackDnsSuffix}", properties: m, headers: m }, type: e }], type: f }, { error: "DualStack is enabled but this partition does not support DualStack", type: d }], type: f }, { endpoint: { url: "https://polly.{Region}.{PartitionResult#dnsSuffix}", properties: m, headers: m }, type: e }], type: f }], type: f }, { error: "Invalid Configuration: Missing Region", type: d }] };
var ruleSet = _data;

// node_modules/@aws-sdk/client-polly/dist-es/endpoint/endpointResolver.js
var cache = new EndpointCache({
  size: 50,
  params: ["Endpoint", "Region", "UseDualStack", "UseFIPS"]
});
var defaultEndpointResolver = (endpointParams, context = {}) => {
  return cache.get(endpointParams, () => resolveEndpoint(ruleSet, {
    endpointParams,
    logger: context.logger
  }));
};
customEndpointFunctions.aws = awsEndpointFunctions;

// node_modules/@aws-sdk/client-polly/dist-es/runtimeConfig.shared.js
var getRuntimeConfig = (config) => {
  return {
    apiVersion: "2016-06-10",
    base64Decoder: (config == null ? void 0 : config.base64Decoder) ?? fromBase64,
    base64Encoder: (config == null ? void 0 : config.base64Encoder) ?? toBase64,
    disableHostPrefix: (config == null ? void 0 : config.disableHostPrefix) ?? false,
    endpointProvider: (config == null ? void 0 : config.endpointProvider) ?? defaultEndpointResolver,
    extensions: (config == null ? void 0 : config.extensions) ?? [],
    httpAuthSchemeProvider: (config == null ? void 0 : config.httpAuthSchemeProvider) ?? defaultPollyHttpAuthSchemeProvider,
    httpAuthSchemes: (config == null ? void 0 : config.httpAuthSchemes) ?? [
      {
        schemeId: "aws.auth#sigv4",
        identityProvider: (ipc) => ipc.getIdentityProvider("aws.auth#sigv4"),
        signer: new AwsSdkSigV4Signer()
      }
    ],
    logger: (config == null ? void 0 : config.logger) ?? new NoOpLogger(),
    sdkStreamMixin: (config == null ? void 0 : config.sdkStreamMixin) ?? sdkStreamMixin,
    serviceId: (config == null ? void 0 : config.serviceId) ?? "Polly",
    urlParser: (config == null ? void 0 : config.urlParser) ?? parseUrl,
    utf8Decoder: (config == null ? void 0 : config.utf8Decoder) ?? fromUtf8,
    utf8Encoder: (config == null ? void 0 : config.utf8Encoder) ?? toUtf8
  };
};

// node_modules/@aws-sdk/client-polly/dist-es/runtimeConfig.browser.js
var getRuntimeConfig2 = (config) => {
  const defaultsMode = resolveDefaultsModeConfig(config);
  const defaultConfigProvider = () => defaultsMode().then(loadConfigsForDefaultMode);
  const clientSharedValues = getRuntimeConfig(config);
  return {
    ...clientSharedValues,
    ...config,
    runtime: "browser",
    defaultsMode,
    bodyLengthChecker: (config == null ? void 0 : config.bodyLengthChecker) ?? calculateBodyLength,
    credentialDefaultProvider: (config == null ? void 0 : config.credentialDefaultProvider) ?? ((_) => () => Promise.reject(new Error("Credential is missing"))),
    defaultUserAgentProvider: (config == null ? void 0 : config.defaultUserAgentProvider) ?? createDefaultUserAgentProvider({ serviceId: clientSharedValues.serviceId, clientVersion: package_default.version }),
    maxAttempts: (config == null ? void 0 : config.maxAttempts) ?? DEFAULT_MAX_ATTEMPTS,
    region: (config == null ? void 0 : config.region) ?? invalidProvider("Region is missing"),
    requestHandler: FetchHttpHandler.create((config == null ? void 0 : config.requestHandler) ?? defaultConfigProvider),
    retryMode: (config == null ? void 0 : config.retryMode) ?? (async () => (await defaultConfigProvider()).retryMode || DEFAULT_RETRY_MODE),
    sha256: (config == null ? void 0 : config.sha256) ?? Sha256,
    streamCollector: (config == null ? void 0 : config.streamCollector) ?? streamCollector,
    useDualstackEndpoint: (config == null ? void 0 : config.useDualstackEndpoint) ?? (() => Promise.resolve(DEFAULT_USE_DUALSTACK_ENDPOINT)),
    useFipsEndpoint: (config == null ? void 0 : config.useFipsEndpoint) ?? (() => Promise.resolve(DEFAULT_USE_FIPS_ENDPOINT))
  };
};

// node_modules/@aws-sdk/client-polly/dist-es/auth/httpAuthExtensionConfiguration.js
var getHttpAuthExtensionConfiguration = (runtimeConfig) => {
  const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;
  let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;
  let _credentials = runtimeConfig.credentials;
  return {
    setHttpAuthScheme(httpAuthScheme) {
      const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);
      if (index === -1) {
        _httpAuthSchemes.push(httpAuthScheme);
      } else {
        _httpAuthSchemes.splice(index, 1, httpAuthScheme);
      }
    },
    httpAuthSchemes() {
      return _httpAuthSchemes;
    },
    setHttpAuthSchemeProvider(httpAuthSchemeProvider) {
      _httpAuthSchemeProvider = httpAuthSchemeProvider;
    },
    httpAuthSchemeProvider() {
      return _httpAuthSchemeProvider;
    },
    setCredentials(credentials) {
      _credentials = credentials;
    },
    credentials() {
      return _credentials;
    }
  };
};
var resolveHttpAuthRuntimeConfig = (config) => {
  return {
    httpAuthSchemes: config.httpAuthSchemes(),
    httpAuthSchemeProvider: config.httpAuthSchemeProvider(),
    credentials: config.credentials()
  };
};

// node_modules/@aws-sdk/client-polly/dist-es/runtimeExtensions.js
var asPartial = (t2) => t2;
var resolveRuntimeExtensions = (runtimeConfig, extensions) => {
  const extensionConfiguration = {
    ...asPartial(getAwsRegionExtensionConfiguration(runtimeConfig)),
    ...asPartial(getDefaultExtensionConfiguration(runtimeConfig)),
    ...asPartial(getHttpHandlerExtensionConfiguration(runtimeConfig)),
    ...asPartial(getHttpAuthExtensionConfiguration(runtimeConfig))
  };
  extensions.forEach((extension) => extension.configure(extensionConfiguration));
  return {
    ...runtimeConfig,
    ...resolveAwsRegionExtensionConfiguration(extensionConfiguration),
    ...resolveDefaultRuntimeConfig(extensionConfiguration),
    ...resolveHttpHandlerRuntimeConfig(extensionConfiguration),
    ...resolveHttpAuthRuntimeConfig(extensionConfiguration)
  };
};

// node_modules/@aws-sdk/client-polly/dist-es/PollyClient.js
var PollyClient = class extends Client {
  constructor(...[configuration]) {
    const _config_0 = getRuntimeConfig2(configuration || {});
    const _config_1 = resolveClientEndpointParameters(_config_0);
    const _config_2 = resolveUserAgentConfig(_config_1);
    const _config_3 = resolveRetryConfig(_config_2);
    const _config_4 = resolveRegionConfig(_config_3);
    const _config_5 = resolveHostHeaderConfig(_config_4);
    const _config_6 = resolveEndpointConfig(_config_5);
    const _config_7 = resolveHttpAuthSchemeConfig(_config_6);
    const _config_8 = resolveRuntimeExtensions(_config_7, (configuration == null ? void 0 : configuration.extensions) || []);
    super(_config_8);
    this.config = _config_8;
    this.middlewareStack.use(getUserAgentPlugin(this.config));
    this.middlewareStack.use(getRetryPlugin(this.config));
    this.middlewareStack.use(getContentLengthPlugin(this.config));
    this.middlewareStack.use(getHostHeaderPlugin(this.config));
    this.middlewareStack.use(getLoggerPlugin(this.config));
    this.middlewareStack.use(getRecursionDetectionPlugin(this.config));
    this.middlewareStack.use(getHttpAuthSchemeEndpointRuleSetPlugin(this.config, {
      httpAuthSchemeParametersProvider: defaultPollyHttpAuthSchemeParametersProvider,
      identityProviderConfigProvider: async (config) => new DefaultIdentityProviderConfig({
        "aws.auth#sigv4": config.credentials
      })
    }));
    this.middlewareStack.use(getHttpSigningPlugin(this.config));
  }
  destroy() {
    super.destroy();
  }
};

// node_modules/@aws-sdk/client-polly/dist-es/models/PollyServiceException.js
var PollyServiceException = class _PollyServiceException extends ServiceException {
  constructor(options) {
    super(options);
    Object.setPrototypeOf(this, _PollyServiceException.prototype);
  }
};

// node_modules/@aws-sdk/client-polly/dist-es/models/models_0.js
var LexiconNotFoundException = class _LexiconNotFoundException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "LexiconNotFoundException",
      $fault: "client",
      ...opts
    });
    this.name = "LexiconNotFoundException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _LexiconNotFoundException.prototype);
  }
};
var ServiceFailureException = class _ServiceFailureException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "ServiceFailureException",
      $fault: "server",
      ...opts
    });
    this.name = "ServiceFailureException";
    this.$fault = "server";
    Object.setPrototypeOf(this, _ServiceFailureException.prototype);
  }
};
var Engine = {
  GENERATIVE: "generative",
  LONG_FORM: "long-form",
  NEURAL: "neural",
  STANDARD: "standard"
};
var LanguageCode = {
  ar_AE: "ar-AE",
  arb: "arb",
  ca_ES: "ca-ES",
  cmn_CN: "cmn-CN",
  cs_CZ: "cs-CZ",
  cy_GB: "cy-GB",
  da_DK: "da-DK",
  de_AT: "de-AT",
  de_CH: "de-CH",
  de_DE: "de-DE",
  en_AU: "en-AU",
  en_GB: "en-GB",
  en_GB_WLS: "en-GB-WLS",
  en_IE: "en-IE",
  en_IN: "en-IN",
  en_NZ: "en-NZ",
  en_US: "en-US",
  en_ZA: "en-ZA",
  es_ES: "es-ES",
  es_MX: "es-MX",
  es_US: "es-US",
  fi_FI: "fi-FI",
  fr_BE: "fr-BE",
  fr_CA: "fr-CA",
  fr_FR: "fr-FR",
  hi_IN: "hi-IN",
  is_IS: "is-IS",
  it_IT: "it-IT",
  ja_JP: "ja-JP",
  ko_KR: "ko-KR",
  nb_NO: "nb-NO",
  nl_BE: "nl-BE",
  nl_NL: "nl-NL",
  pl_PL: "pl-PL",
  pt_BR: "pt-BR",
  pt_PT: "pt-PT",
  ro_RO: "ro-RO",
  ru_RU: "ru-RU",
  sv_SE: "sv-SE",
  tr_TR: "tr-TR",
  yue_CN: "yue-CN"
};
var Gender = {
  Female: "Female",
  Male: "Male"
};
var VoiceId = {
  Aditi: "Aditi",
  Adriano: "Adriano",
  Amy: "Amy",
  Andres: "Andres",
  Aria: "Aria",
  Arlet: "Arlet",
  Arthur: "Arthur",
  Astrid: "Astrid",
  Ayanda: "Ayanda",
  Bianca: "Bianca",
  Brian: "Brian",
  Burcu: "Burcu",
  Camila: "Camila",
  Carla: "Carla",
  Carmen: "Carmen",
  Celine: "Celine",
  Chantal: "Chantal",
  Conchita: "Conchita",
  Cristiano: "Cristiano",
  Daniel: "Daniel",
  Danielle: "Danielle",
  Dora: "Dora",
  Elin: "Elin",
  Emma: "Emma",
  Enrique: "Enrique",
  Ewa: "Ewa",
  Filiz: "Filiz",
  Gabrielle: "Gabrielle",
  Geraint: "Geraint",
  Giorgio: "Giorgio",
  Gregory: "Gregory",
  Gwyneth: "Gwyneth",
  Hala: "Hala",
  Hannah: "Hannah",
  Hans: "Hans",
  Hiujin: "Hiujin",
  Ida: "Ida",
  Ines: "Ines",
  Isabelle: "Isabelle",
  Ivy: "Ivy",
  Jacek: "Jacek",
  Jan: "Jan",
  Jitka: "Jitka",
  Joanna: "Joanna",
  Joey: "Joey",
  Justin: "Justin",
  Kajal: "Kajal",
  Karl: "Karl",
  Kazuha: "Kazuha",
  Kendra: "Kendra",
  Kevin: "Kevin",
  Kimberly: "Kimberly",
  Laura: "Laura",
  Lea: "Lea",
  Liam: "Liam",
  Lisa: "Lisa",
  Liv: "Liv",
  Lotte: "Lotte",
  Lucia: "Lucia",
  Lupe: "Lupe",
  Mads: "Mads",
  Maja: "Maja",
  Marlene: "Marlene",
  Mathieu: "Mathieu",
  Matthew: "Matthew",
  Maxim: "Maxim",
  Mia: "Mia",
  Miguel: "Miguel",
  Mizuki: "Mizuki",
  Naja: "Naja",
  Niamh: "Niamh",
  Nicole: "Nicole",
  Ola: "Ola",
  Olivia: "Olivia",
  Pedro: "Pedro",
  Penelope: "Penelope",
  Raveena: "Raveena",
  Remi: "Remi",
  Ricardo: "Ricardo",
  Ruben: "Ruben",
  Russell: "Russell",
  Ruth: "Ruth",
  Sabrina: "Sabrina",
  Salli: "Salli",
  Seoyeon: "Seoyeon",
  Sergio: "Sergio",
  Sofie: "Sofie",
  Stephen: "Stephen",
  Suvi: "Suvi",
  Takumi: "Takumi",
  Tatyana: "Tatyana",
  Thiago: "Thiago",
  Tomoko: "Tomoko",
  Vicki: "Vicki",
  Vitoria: "Vitoria",
  Zayd: "Zayd",
  Zeina: "Zeina",
  Zhiyu: "Zhiyu"
};
var InvalidNextTokenException = class _InvalidNextTokenException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "InvalidNextTokenException",
      $fault: "client",
      ...opts
    });
    this.name = "InvalidNextTokenException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _InvalidNextTokenException.prototype);
  }
};
var EngineNotSupportedException = class _EngineNotSupportedException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "EngineNotSupportedException",
      $fault: "client",
      ...opts
    });
    this.name = "EngineNotSupportedException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _EngineNotSupportedException.prototype);
  }
};
var OutputFormat = {
  JSON: "json",
  MP3: "mp3",
  OGG_VORBIS: "ogg_vorbis",
  PCM: "pcm"
};
var SpeechMarkType = {
  SENTENCE: "sentence",
  SSML: "ssml",
  VISEME: "viseme",
  WORD: "word"
};
var TaskStatus = {
  COMPLETED: "completed",
  FAILED: "failed",
  IN_PROGRESS: "inProgress",
  SCHEDULED: "scheduled"
};
var TextType = {
  SSML: "ssml",
  TEXT: "text"
};
var InvalidTaskIdException = class _InvalidTaskIdException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "InvalidTaskIdException",
      $fault: "client",
      ...opts
    });
    this.name = "InvalidTaskIdException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _InvalidTaskIdException.prototype);
  }
};
var SynthesisTaskNotFoundException = class _SynthesisTaskNotFoundException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "SynthesisTaskNotFoundException",
      $fault: "client",
      ...opts
    });
    this.name = "SynthesisTaskNotFoundException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _SynthesisTaskNotFoundException.prototype);
  }
};
var InvalidLexiconException = class _InvalidLexiconException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "InvalidLexiconException",
      $fault: "client",
      ...opts
    });
    this.name = "InvalidLexiconException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _InvalidLexiconException.prototype);
  }
};
var InvalidS3BucketException = class _InvalidS3BucketException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "InvalidS3BucketException",
      $fault: "client",
      ...opts
    });
    this.name = "InvalidS3BucketException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _InvalidS3BucketException.prototype);
  }
};
var InvalidS3KeyException = class _InvalidS3KeyException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "InvalidS3KeyException",
      $fault: "client",
      ...opts
    });
    this.name = "InvalidS3KeyException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _InvalidS3KeyException.prototype);
  }
};
var InvalidSampleRateException = class _InvalidSampleRateException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "InvalidSampleRateException",
      $fault: "client",
      ...opts
    });
    this.name = "InvalidSampleRateException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _InvalidSampleRateException.prototype);
  }
};
var InvalidSnsTopicArnException = class _InvalidSnsTopicArnException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "InvalidSnsTopicArnException",
      $fault: "client",
      ...opts
    });
    this.name = "InvalidSnsTopicArnException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _InvalidSnsTopicArnException.prototype);
  }
};
var InvalidSsmlException = class _InvalidSsmlException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "InvalidSsmlException",
      $fault: "client",
      ...opts
    });
    this.name = "InvalidSsmlException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _InvalidSsmlException.prototype);
  }
};
var LanguageNotSupportedException = class _LanguageNotSupportedException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "LanguageNotSupportedException",
      $fault: "client",
      ...opts
    });
    this.name = "LanguageNotSupportedException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _LanguageNotSupportedException.prototype);
  }
};
var LexiconSizeExceededException = class _LexiconSizeExceededException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "LexiconSizeExceededException",
      $fault: "client",
      ...opts
    });
    this.name = "LexiconSizeExceededException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _LexiconSizeExceededException.prototype);
  }
};
var MarksNotSupportedForFormatException = class _MarksNotSupportedForFormatException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "MarksNotSupportedForFormatException",
      $fault: "client",
      ...opts
    });
    this.name = "MarksNotSupportedForFormatException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _MarksNotSupportedForFormatException.prototype);
  }
};
var MaxLexemeLengthExceededException = class _MaxLexemeLengthExceededException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "MaxLexemeLengthExceededException",
      $fault: "client",
      ...opts
    });
    this.name = "MaxLexemeLengthExceededException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _MaxLexemeLengthExceededException.prototype);
  }
};
var MaxLexiconsNumberExceededException = class _MaxLexiconsNumberExceededException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "MaxLexiconsNumberExceededException",
      $fault: "client",
      ...opts
    });
    this.name = "MaxLexiconsNumberExceededException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _MaxLexiconsNumberExceededException.prototype);
  }
};
var UnsupportedPlsAlphabetException = class _UnsupportedPlsAlphabetException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "UnsupportedPlsAlphabetException",
      $fault: "client",
      ...opts
    });
    this.name = "UnsupportedPlsAlphabetException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _UnsupportedPlsAlphabetException.prototype);
  }
};
var UnsupportedPlsLanguageException = class _UnsupportedPlsLanguageException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "UnsupportedPlsLanguageException",
      $fault: "client",
      ...opts
    });
    this.name = "UnsupportedPlsLanguageException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _UnsupportedPlsLanguageException.prototype);
  }
};
var SsmlMarksNotSupportedForTextTypeException = class _SsmlMarksNotSupportedForTextTypeException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "SsmlMarksNotSupportedForTextTypeException",
      $fault: "client",
      ...opts
    });
    this.name = "SsmlMarksNotSupportedForTextTypeException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _SsmlMarksNotSupportedForTextTypeException.prototype);
  }
};
var TextLengthExceededException = class _TextLengthExceededException extends PollyServiceException {
  constructor(opts) {
    super({
      name: "TextLengthExceededException",
      $fault: "client",
      ...opts
    });
    this.name = "TextLengthExceededException";
    this.$fault = "client";
    Object.setPrototypeOf(this, _TextLengthExceededException.prototype);
  }
};
var LexiconFilterSensitiveLog = (obj) => ({
  ...obj,
  ...obj.Content && { Content: SENSITIVE_STRING }
});
var GetLexiconOutputFilterSensitiveLog = (obj) => ({
  ...obj,
  ...obj.Lexicon && { Lexicon: LexiconFilterSensitiveLog(obj.Lexicon) }
});
var PutLexiconInputFilterSensitiveLog = (obj) => ({
  ...obj,
  ...obj.Content && { Content: SENSITIVE_STRING }
});
var SynthesizeSpeechOutputFilterSensitiveLog = (obj) => ({
  ...obj
});

// node_modules/@aws-sdk/client-polly/dist-es/protocols/Aws_restJson1.js
var se_DeleteLexiconCommand = async (input, context) => {
  const b2 = requestBuilder(input, context);
  const headers = {};
  b2.bp("/v1/lexicons/{Name}");
  b2.p("Name", () => input.Name, "{Name}", false);
  let body;
  b2.m("DELETE").h(headers).b(body);
  return b2.build();
};
var se_DescribeVoicesCommand = async (input, context) => {
  const b2 = requestBuilder(input, context);
  const headers = {};
  b2.bp("/v1/voices");
  const query = map({
    [_E]: [, input[_E]],
    [_LC]: [, input[_LC]],
    [_IALC]: [() => input.IncludeAdditionalLanguageCodes !== void 0, () => input[_IALC].toString()],
    [_NT]: [, input[_NT]]
  });
  let body;
  b2.m("GET").h(headers).q(query).b(body);
  return b2.build();
};
var se_GetLexiconCommand = async (input, context) => {
  const b2 = requestBuilder(input, context);
  const headers = {};
  b2.bp("/v1/lexicons/{Name}");
  b2.p("Name", () => input.Name, "{Name}", false);
  let body;
  b2.m("GET").h(headers).b(body);
  return b2.build();
};
var se_GetSpeechSynthesisTaskCommand = async (input, context) => {
  const b2 = requestBuilder(input, context);
  const headers = {};
  b2.bp("/v1/synthesisTasks/{TaskId}");
  b2.p("TaskId", () => input.TaskId, "{TaskId}", false);
  let body;
  b2.m("GET").h(headers).b(body);
  return b2.build();
};
var se_ListLexiconsCommand = async (input, context) => {
  const b2 = requestBuilder(input, context);
  const headers = {};
  b2.bp("/v1/lexicons");
  const query = map({
    [_NT]: [, input[_NT]]
  });
  let body;
  b2.m("GET").h(headers).q(query).b(body);
  return b2.build();
};
var se_ListSpeechSynthesisTasksCommand = async (input, context) => {
  const b2 = requestBuilder(input, context);
  const headers = {};
  b2.bp("/v1/synthesisTasks");
  const query = map({
    [_MR]: [() => input.MaxResults !== void 0, () => input[_MR].toString()],
    [_NT]: [, input[_NT]],
    [_S]: [, input[_S]]
  });
  let body;
  b2.m("GET").h(headers).q(query).b(body);
  return b2.build();
};
var se_PutLexiconCommand = async (input, context) => {
  const b2 = requestBuilder(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b2.bp("/v1/lexicons/{Name}");
  b2.p("Name", () => input.Name, "{Name}", false);
  let body;
  body = JSON.stringify(take(input, {
    Content: []
  }));
  b2.m("PUT").h(headers).b(body);
  return b2.build();
};
var se_StartSpeechSynthesisTaskCommand = async (input, context) => {
  const b2 = requestBuilder(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b2.bp("/v1/synthesisTasks");
  let body;
  body = JSON.stringify(take(input, {
    Engine: [],
    LanguageCode: [],
    LexiconNames: (_) => _json(_),
    OutputFormat: [],
    OutputS3BucketName: [],
    OutputS3KeyPrefix: [],
    SampleRate: [],
    SnsTopicArn: [],
    SpeechMarkTypes: (_) => _json(_),
    Text: [],
    TextType: [],
    VoiceId: []
  }));
  b2.m("POST").h(headers).b(body);
  return b2.build();
};
var se_SynthesizeSpeechCommand = async (input, context) => {
  const b2 = requestBuilder(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b2.bp("/v1/speech");
  let body;
  body = JSON.stringify(take(input, {
    Engine: [],
    LanguageCode: [],
    LexiconNames: (_) => _json(_),
    OutputFormat: [],
    SampleRate: [],
    SpeechMarkTypes: (_) => _json(_),
    Text: [],
    TextType: [],
    VoiceId: []
  }));
  b2.m("POST").h(headers).b(body);
  return b2.build();
};
var de_DeleteLexiconCommand = async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = map({
    $metadata: deserializeMetadata(output)
  });
  await collectBody(output.body, context);
  return contents;
};
var de_DescribeVoicesCommand = async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = map({
    $metadata: deserializeMetadata(output)
  });
  const data = expectNonNull(expectObject(await parseJsonBody(output.body, context)), "body");
  const doc = take(data, {
    NextToken: expectString,
    Voices: _json
  });
  Object.assign(contents, doc);
  return contents;
};
var de_GetLexiconCommand = async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = map({
    $metadata: deserializeMetadata(output)
  });
  const data = expectNonNull(expectObject(await parseJsonBody(output.body, context)), "body");
  const doc = take(data, {
    Lexicon: _json,
    LexiconAttributes: (_) => de_LexiconAttributes(_, context)
  });
  Object.assign(contents, doc);
  return contents;
};
var de_GetSpeechSynthesisTaskCommand = async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = map({
    $metadata: deserializeMetadata(output)
  });
  const data = expectNonNull(expectObject(await parseJsonBody(output.body, context)), "body");
  const doc = take(data, {
    SynthesisTask: (_) => de_SynthesisTask(_, context)
  });
  Object.assign(contents, doc);
  return contents;
};
var de_ListLexiconsCommand = async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = map({
    $metadata: deserializeMetadata(output)
  });
  const data = expectNonNull(expectObject(await parseJsonBody(output.body, context)), "body");
  const doc = take(data, {
    Lexicons: (_) => de_LexiconDescriptionList(_, context),
    NextToken: expectString
  });
  Object.assign(contents, doc);
  return contents;
};
var de_ListSpeechSynthesisTasksCommand = async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = map({
    $metadata: deserializeMetadata(output)
  });
  const data = expectNonNull(expectObject(await parseJsonBody(output.body, context)), "body");
  const doc = take(data, {
    NextToken: expectString,
    SynthesisTasks: (_) => de_SynthesisTasks(_, context)
  });
  Object.assign(contents, doc);
  return contents;
};
var de_PutLexiconCommand = async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = map({
    $metadata: deserializeMetadata(output)
  });
  await collectBody(output.body, context);
  return contents;
};
var de_StartSpeechSynthesisTaskCommand = async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = map({
    $metadata: deserializeMetadata(output)
  });
  const data = expectNonNull(expectObject(await parseJsonBody(output.body, context)), "body");
  const doc = take(data, {
    SynthesisTask: (_) => de_SynthesisTask(_, context)
  });
  Object.assign(contents, doc);
  return contents;
};
var de_SynthesizeSpeechCommand = async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = map({
    $metadata: deserializeMetadata(output),
    [_CT]: [, output.headers[_ct]],
    [_RC]: [() => void 0 !== output.headers[_xar], () => strictParseInt32(output.headers[_xar])]
  });
  const data = output.body;
  context.sdkStreamMixin(data);
  contents.AudioStream = data;
  return contents;
};
var de_CommandError = async (output, context) => {
  const parsedOutput = {
    ...output,
    body: await parseJsonErrorBody(output.body, context)
  };
  const errorCode = loadRestJsonErrorCode(output, parsedOutput.body);
  switch (errorCode) {
    case "LexiconNotFoundException":
    case "com.amazonaws.polly#LexiconNotFoundException":
      throw await de_LexiconNotFoundExceptionRes(parsedOutput, context);
    case "ServiceFailureException":
    case "com.amazonaws.polly#ServiceFailureException":
      throw await de_ServiceFailureExceptionRes(parsedOutput, context);
    case "InvalidNextTokenException":
    case "com.amazonaws.polly#InvalidNextTokenException":
      throw await de_InvalidNextTokenExceptionRes(parsedOutput, context);
    case "InvalidTaskIdException":
    case "com.amazonaws.polly#InvalidTaskIdException":
      throw await de_InvalidTaskIdExceptionRes(parsedOutput, context);
    case "SynthesisTaskNotFoundException":
    case "com.amazonaws.polly#SynthesisTaskNotFoundException":
      throw await de_SynthesisTaskNotFoundExceptionRes(parsedOutput, context);
    case "InvalidLexiconException":
    case "com.amazonaws.polly#InvalidLexiconException":
      throw await de_InvalidLexiconExceptionRes(parsedOutput, context);
    case "LexiconSizeExceededException":
    case "com.amazonaws.polly#LexiconSizeExceededException":
      throw await de_LexiconSizeExceededExceptionRes(parsedOutput, context);
    case "MaxLexemeLengthExceededException":
    case "com.amazonaws.polly#MaxLexemeLengthExceededException":
      throw await de_MaxLexemeLengthExceededExceptionRes(parsedOutput, context);
    case "MaxLexiconsNumberExceededException":
    case "com.amazonaws.polly#MaxLexiconsNumberExceededException":
      throw await de_MaxLexiconsNumberExceededExceptionRes(parsedOutput, context);
    case "UnsupportedPlsAlphabetException":
    case "com.amazonaws.polly#UnsupportedPlsAlphabetException":
      throw await de_UnsupportedPlsAlphabetExceptionRes(parsedOutput, context);
    case "UnsupportedPlsLanguageException":
    case "com.amazonaws.polly#UnsupportedPlsLanguageException":
      throw await de_UnsupportedPlsLanguageExceptionRes(parsedOutput, context);
    case "EngineNotSupportedException":
    case "com.amazonaws.polly#EngineNotSupportedException":
      throw await de_EngineNotSupportedExceptionRes(parsedOutput, context);
    case "InvalidS3BucketException":
    case "com.amazonaws.polly#InvalidS3BucketException":
      throw await de_InvalidS3BucketExceptionRes(parsedOutput, context);
    case "InvalidS3KeyException":
    case "com.amazonaws.polly#InvalidS3KeyException":
      throw await de_InvalidS3KeyExceptionRes(parsedOutput, context);
    case "InvalidSampleRateException":
    case "com.amazonaws.polly#InvalidSampleRateException":
      throw await de_InvalidSampleRateExceptionRes(parsedOutput, context);
    case "InvalidSnsTopicArnException":
    case "com.amazonaws.polly#InvalidSnsTopicArnException":
      throw await de_InvalidSnsTopicArnExceptionRes(parsedOutput, context);
    case "InvalidSsmlException":
    case "com.amazonaws.polly#InvalidSsmlException":
      throw await de_InvalidSsmlExceptionRes(parsedOutput, context);
    case "LanguageNotSupportedException":
    case "com.amazonaws.polly#LanguageNotSupportedException":
      throw await de_LanguageNotSupportedExceptionRes(parsedOutput, context);
    case "MarksNotSupportedForFormatException":
    case "com.amazonaws.polly#MarksNotSupportedForFormatException":
      throw await de_MarksNotSupportedForFormatExceptionRes(parsedOutput, context);
    case "SsmlMarksNotSupportedForTextTypeException":
    case "com.amazonaws.polly#SsmlMarksNotSupportedForTextTypeException":
      throw await de_SsmlMarksNotSupportedForTextTypeExceptionRes(parsedOutput, context);
    case "TextLengthExceededException":
    case "com.amazonaws.polly#TextLengthExceededException":
      throw await de_TextLengthExceededExceptionRes(parsedOutput, context);
    default:
      const parsedBody = parsedOutput.body;
      return throwDefaultError({
        output,
        parsedBody,
        errorCode
      });
  }
};
var throwDefaultError = withBaseException(PollyServiceException);
var de_EngineNotSupportedExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new EngineNotSupportedException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_InvalidLexiconExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new InvalidLexiconException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_InvalidNextTokenExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new InvalidNextTokenException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_InvalidS3BucketExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new InvalidS3BucketException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_InvalidS3KeyExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new InvalidS3KeyException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_InvalidSampleRateExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new InvalidSampleRateException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_InvalidSnsTopicArnExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new InvalidSnsTopicArnException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_InvalidSsmlExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new InvalidSsmlException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_InvalidTaskIdExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new InvalidTaskIdException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_LanguageNotSupportedExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new LanguageNotSupportedException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_LexiconNotFoundExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new LexiconNotFoundException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_LexiconSizeExceededExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new LexiconSizeExceededException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_MarksNotSupportedForFormatExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new MarksNotSupportedForFormatException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_MaxLexemeLengthExceededExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new MaxLexemeLengthExceededException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_MaxLexiconsNumberExceededExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new MaxLexiconsNumberExceededException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_ServiceFailureExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new ServiceFailureException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_SsmlMarksNotSupportedForTextTypeExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new SsmlMarksNotSupportedForTextTypeException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_SynthesisTaskNotFoundExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new SynthesisTaskNotFoundException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_TextLengthExceededExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new TextLengthExceededException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_UnsupportedPlsAlphabetExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new UnsupportedPlsAlphabetException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_UnsupportedPlsLanguageExceptionRes = async (parsedOutput, context) => {
  const contents = map({});
  const data = parsedOutput.body;
  const doc = take(data, {
    message: expectString
  });
  Object.assign(contents, doc);
  const exception = new UnsupportedPlsLanguageException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return decorateServiceException(exception, parsedOutput.body);
};
var de_LexiconAttributes = (output, context) => {
  return take(output, {
    Alphabet: expectString,
    LanguageCode: expectString,
    LastModified: (_) => expectNonNull(parseEpochTimestamp(expectNumber(_))),
    LexemesCount: expectInt32,
    LexiconArn: expectString,
    Size: expectInt32
  });
};
var de_LexiconDescription = (output, context) => {
  return take(output, {
    Attributes: (_) => de_LexiconAttributes(_, context),
    Name: expectString
  });
};
var de_LexiconDescriptionList = (output, context) => {
  const retVal = (output || []).filter((e2) => e2 != null).map((entry) => {
    return de_LexiconDescription(entry, context);
  });
  return retVal;
};
var de_SynthesisTask = (output, context) => {
  return take(output, {
    CreationTime: (_) => expectNonNull(parseEpochTimestamp(expectNumber(_))),
    Engine: expectString,
    LanguageCode: expectString,
    LexiconNames: _json,
    OutputFormat: expectString,
    OutputUri: expectString,
    RequestCharacters: expectInt32,
    SampleRate: expectString,
    SnsTopicArn: expectString,
    SpeechMarkTypes: _json,
    TaskId: expectString,
    TaskStatus: expectString,
    TaskStatusReason: expectString,
    TextType: expectString,
    VoiceId: expectString
  });
};
var de_SynthesisTasks = (output, context) => {
  const retVal = (output || []).filter((e2) => e2 != null).map((entry) => {
    return de_SynthesisTask(entry, context);
  });
  return retVal;
};
var deserializeMetadata = (output) => ({
  httpStatusCode: output.statusCode,
  requestId: output.headers["x-amzn-requestid"] ?? output.headers["x-amzn-request-id"] ?? output.headers["x-amz-request-id"],
  extendedRequestId: output.headers["x-amz-id-2"],
  cfId: output.headers["x-amz-cf-id"]
});
var _CT = "ContentType";
var _E = "Engine";
var _IALC = "IncludeAdditionalLanguageCodes";
var _LC = "LanguageCode";
var _MR = "MaxResults";
var _NT = "NextToken";
var _RC = "RequestCharacters";
var _S = "Status";
var _ct = "content-type";
var _xar = "x-amzn-requestcharacters";

// node_modules/@aws-sdk/client-polly/dist-es/commands/DeleteLexiconCommand.js
var DeleteLexiconCommand = class extends Command.classBuilder().ep(commonParams).m(function(Command2, cs, config, o2) {
  return [
    getSerdePlugin(config, this.serialize, this.deserialize),
    getEndpointPlugin(config, Command2.getEndpointParameterInstructions())
  ];
}).s("Parrot_v1", "DeleteLexicon", {}).n("PollyClient", "DeleteLexiconCommand").f(void 0, void 0).ser(se_DeleteLexiconCommand).de(de_DeleteLexiconCommand).build() {
};

// node_modules/@aws-sdk/client-polly/dist-es/commands/DescribeVoicesCommand.js
var DescribeVoicesCommand = class extends Command.classBuilder().ep(commonParams).m(function(Command2, cs, config, o2) {
  return [
    getSerdePlugin(config, this.serialize, this.deserialize),
    getEndpointPlugin(config, Command2.getEndpointParameterInstructions())
  ];
}).s("Parrot_v1", "DescribeVoices", {}).n("PollyClient", "DescribeVoicesCommand").f(void 0, void 0).ser(se_DescribeVoicesCommand).de(de_DescribeVoicesCommand).build() {
};

// node_modules/@aws-sdk/client-polly/dist-es/commands/GetLexiconCommand.js
var GetLexiconCommand = class extends Command.classBuilder().ep(commonParams).m(function(Command2, cs, config, o2) {
  return [
    getSerdePlugin(config, this.serialize, this.deserialize),
    getEndpointPlugin(config, Command2.getEndpointParameterInstructions())
  ];
}).s("Parrot_v1", "GetLexicon", {}).n("PollyClient", "GetLexiconCommand").f(void 0, GetLexiconOutputFilterSensitiveLog).ser(se_GetLexiconCommand).de(de_GetLexiconCommand).build() {
};

// node_modules/@aws-sdk/client-polly/dist-es/commands/GetSpeechSynthesisTaskCommand.js
var GetSpeechSynthesisTaskCommand = class extends Command.classBuilder().ep(commonParams).m(function(Command2, cs, config, o2) {
  return [
    getSerdePlugin(config, this.serialize, this.deserialize),
    getEndpointPlugin(config, Command2.getEndpointParameterInstructions())
  ];
}).s("Parrot_v1", "GetSpeechSynthesisTask", {}).n("PollyClient", "GetSpeechSynthesisTaskCommand").f(void 0, void 0).ser(se_GetSpeechSynthesisTaskCommand).de(de_GetSpeechSynthesisTaskCommand).build() {
};

// node_modules/@aws-sdk/client-polly/dist-es/commands/ListLexiconsCommand.js
var ListLexiconsCommand = class extends Command.classBuilder().ep(commonParams).m(function(Command2, cs, config, o2) {
  return [
    getSerdePlugin(config, this.serialize, this.deserialize),
    getEndpointPlugin(config, Command2.getEndpointParameterInstructions())
  ];
}).s("Parrot_v1", "ListLexicons", {}).n("PollyClient", "ListLexiconsCommand").f(void 0, void 0).ser(se_ListLexiconsCommand).de(de_ListLexiconsCommand).build() {
};

// node_modules/@aws-sdk/client-polly/dist-es/commands/ListSpeechSynthesisTasksCommand.js
var ListSpeechSynthesisTasksCommand = class extends Command.classBuilder().ep(commonParams).m(function(Command2, cs, config, o2) {
  return [
    getSerdePlugin(config, this.serialize, this.deserialize),
    getEndpointPlugin(config, Command2.getEndpointParameterInstructions())
  ];
}).s("Parrot_v1", "ListSpeechSynthesisTasks", {}).n("PollyClient", "ListSpeechSynthesisTasksCommand").f(void 0, void 0).ser(se_ListSpeechSynthesisTasksCommand).de(de_ListSpeechSynthesisTasksCommand).build() {
};

// node_modules/@aws-sdk/client-polly/dist-es/commands/PutLexiconCommand.js
var PutLexiconCommand = class extends Command.classBuilder().ep(commonParams).m(function(Command2, cs, config, o2) {
  return [
    getSerdePlugin(config, this.serialize, this.deserialize),
    getEndpointPlugin(config, Command2.getEndpointParameterInstructions())
  ];
}).s("Parrot_v1", "PutLexicon", {}).n("PollyClient", "PutLexiconCommand").f(PutLexiconInputFilterSensitiveLog, void 0).ser(se_PutLexiconCommand).de(de_PutLexiconCommand).build() {
};

// node_modules/@aws-sdk/client-polly/dist-es/commands/StartSpeechSynthesisTaskCommand.js
var StartSpeechSynthesisTaskCommand = class extends Command.classBuilder().ep(commonParams).m(function(Command2, cs, config, o2) {
  return [
    getSerdePlugin(config, this.serialize, this.deserialize),
    getEndpointPlugin(config, Command2.getEndpointParameterInstructions())
  ];
}).s("Parrot_v1", "StartSpeechSynthesisTask", {}).n("PollyClient", "StartSpeechSynthesisTaskCommand").f(void 0, void 0).ser(se_StartSpeechSynthesisTaskCommand).de(de_StartSpeechSynthesisTaskCommand).build() {
};

// node_modules/@aws-sdk/client-polly/dist-es/commands/SynthesizeSpeechCommand.js
var SynthesizeSpeechCommand = class extends Command.classBuilder().ep(commonParams).m(function(Command2, cs, config, o2) {
  return [
    getSerdePlugin(config, this.serialize, this.deserialize),
    getEndpointPlugin(config, Command2.getEndpointParameterInstructions())
  ];
}).s("Parrot_v1", "SynthesizeSpeech", {}).n("PollyClient", "SynthesizeSpeechCommand").f(void 0, SynthesizeSpeechOutputFilterSensitiveLog).ser(se_SynthesizeSpeechCommand).de(de_SynthesizeSpeechCommand).build() {
};

// node_modules/@aws-sdk/client-polly/dist-es/Polly.js
var commands = {
  DeleteLexiconCommand,
  DescribeVoicesCommand,
  GetLexiconCommand,
  GetSpeechSynthesisTaskCommand,
  ListLexiconsCommand,
  ListSpeechSynthesisTasksCommand,
  PutLexiconCommand,
  StartSpeechSynthesisTaskCommand,
  SynthesizeSpeechCommand
};
var Polly = class extends PollyClient {
};
createAggregatedClient(commands, Polly);

// node_modules/@aws-sdk/client-polly/dist-es/pagination/ListSpeechSynthesisTasksPaginator.js
var paginateListSpeechSynthesisTasks = createPaginator(PollyClient, ListSpeechSynthesisTasksCommand, "NextToken", "NextToken", "MaxResults");
export {
  Command as $Command,
  DeleteLexiconCommand,
  DescribeVoicesCommand,
  Engine,
  EngineNotSupportedException,
  Gender,
  GetLexiconCommand,
  GetLexiconOutputFilterSensitiveLog,
  GetSpeechSynthesisTaskCommand,
  InvalidLexiconException,
  InvalidNextTokenException,
  InvalidS3BucketException,
  InvalidS3KeyException,
  InvalidSampleRateException,
  InvalidSnsTopicArnException,
  InvalidSsmlException,
  InvalidTaskIdException,
  LanguageCode,
  LanguageNotSupportedException,
  LexiconFilterSensitiveLog,
  LexiconNotFoundException,
  LexiconSizeExceededException,
  ListLexiconsCommand,
  ListSpeechSynthesisTasksCommand,
  MarksNotSupportedForFormatException,
  MaxLexemeLengthExceededException,
  MaxLexiconsNumberExceededException,
  OutputFormat,
  Polly,
  PollyClient,
  PollyServiceException,
  PutLexiconCommand,
  PutLexiconInputFilterSensitiveLog,
  ServiceFailureException,
  SpeechMarkType,
  SsmlMarksNotSupportedForTextTypeException,
  StartSpeechSynthesisTaskCommand,
  SynthesisTaskNotFoundException,
  SynthesizeSpeechCommand,
  SynthesizeSpeechOutputFilterSensitiveLog,
  TaskStatus,
  TextLengthExceededException,
  TextType,
  UnsupportedPlsAlphabetException,
  UnsupportedPlsLanguageException,
  VoiceId,
  Client as __Client,
  paginateListSpeechSynthesisTasks
};
//# sourceMappingURL=@aws-sdk_client-polly.js.map
