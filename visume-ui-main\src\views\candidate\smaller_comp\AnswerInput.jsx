import React, { useState } from "react";
import CodeMirror from "@uiw/react-codemirror";
// import { P } from '@clerk/clerk-react/dist/controlComponents-CzpRUsyv';
import { Clock, Mic, ArrowRight, LogOut } from "lucide-react";

export default function AnswerInput({
  question,
  isListening,
  handleSpeechRecognition,
  handleNextQuestion,
  onEndInterview,
  currentIndex,
  questions,
  currentQuestion,
  isSpeaking,
  remainingTime,
  isProcessing,
  startAnswering,
  updateAnswer,
  isInterviewActive = false, // New prop to control when interactions are allowed
}) {
  // const [codeAnswer, setCodeAnswer] = useState("")
  return (
    <div className="relative flex h-1/3 w-full flex-grow flex-col items-center justify-center rounded-xl border-2 border-green-300 bg-white p-4 shadow-md">
      <div className="left-4 top-4 mb-2 flex w-full justify-between items-center">
        <h3 className="text-lg font-bold text-green-800">
          <span id="question-type">
            {question?.type
              ? question.type.charAt(0).toUpperCase() + question.type.slice(1)
              : "Interview"}{" "}
            Question
          </span>
        </h3>
        <div className="flex items-center space-x-4">
          {/* Timer */}
          {!isSpeaking && remainingTime > 0 && (
            <div className="flex items-center justify-center space-x-2 text-center text-xl font-bold text-red-600">
              <Clock className="h-6 w-6 text-red-600" />
              <span>{remainingTime}s</span>
            </div>
          )}
        </div>
      </div>
      <div
        className="flex h-full w-full max-w-4xl items-center justify-center gap-2 px-0"
        id="answer-box"
      >
        {(question?.type || "").toLowerCase() === "behavioral" ? (
          <>
            {/* Show auto-recording message when AI is speaking */}
            {isSpeaking && (
              <div className="text-center text-gray-600">
                <p>Recording will start automatically after narration...</p>
              </div>
            )}
            {/* Manual recording button as fallback */}
            {!isListening && !isSpeaking && (
              <button
                id="speak"
                className="btn mb-6 flex items-center justify-center rounded-full bg-blue-500 p-3 px-8 font-medium text-white hover:text-white"
                onClick={() => {
                  if (isInterviewActive) {
                    startAnswering();
                    handleSpeechRecognition();
                  }
                }}
                disabled={!isInterviewActive}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1.25em"
                  height="1.25em"
                  viewBox="0 0 24 24"
                  className="mr-1 inline-block align-middle"
                >
                  <path
                    fill="white"
                    d="M12 14q-1.25 0-2.125-.875T9 11V5q0-1.25.875-2.125T12 2t2.125.875T15 5v6q0 1.25-.875 2.125T12 14m-1 7v-3.075q-2.6-.35-4.3-2.325T5 11h2q0 2.075 1.463 3.538T12 16t3.538-1.463T17 11h2q0 2.625-1.7 4.6T13 17.925V21z"
                  />
                </svg>
                <span className="inline-block align-middle">Answer</span>
              </button>
            )}
            {isListening && (
              // <div
              //   className="flex h-full w-full flex-col items-center justify-center self-center"
              //   id="listening"
              // >
              //   {/* <div className="boxContainer">
              //     {[...Array(15)].map((_, i) => (
              //       <div key={i} className={`box box${i + 1}`} />
              //     ))}
              //   </div> */}
              //   <div className="mt-4 text-center text-2xl font-bold text-blue-600">
              //     Listening...
              //   </div>
              // </div>
              <div
                className="flex h-full w-full flex-col items-center justify-center self-center"
                id="listening"
              >
                <Mic className="h-16 w-16 text-blue-600 animate-pulse" />

                <div className="mt-4 text-center text-2xl font-bold text-blue-600">
                  Listening...
                </div>
              </div>
            )}
          </>
        ) : (question?.type || "").toLowerCase() === "technical" ? (
          <div className="flex h-full w-full flex-col">
            <div
              className="shadow-black/40 outline-black flex w-full flex-col gap-4 rounded-2xl border-4 border-gray-400/20 bg-gray-800 p-2 shadow-xl outline-1"
              id="editor-container"
              style={{ height: "60vh" }}
            >
              <CodeMirror
                onChange={async (value) => {
                  try {
                    if (!isInterviewActive) {
                      return;
                    }
                    if (value && !currentQuestion?.startTimestamp) {
                      startAnswering();
                    }
                    if (typeof updateAnswer !== 'function') {
                      console.error('updateAnswer is not a function:', updateAnswer);
                      return;
                    }
                    await updateAnswer(value);
                    // Wait for state update
                    await new Promise(resolve => setTimeout(resolve, 100));
                  } catch (error) {
                    console.error('Error updating answer:', error);
                  }
                }}
                value={currentQuestion?.answer || ''}
                theme="dark"
                height="100%"
                readOnly={!isInterviewActive}
              />
            </div>
          </div>
        ) : (
          <div className="flex h-full w-full flex-col">
              <div
                className="shadow-black/40 outline-black flex w-full flex-col gap-4 rounded-2xl border-4 border-gray-400/20 bg-gray-800 p-2 shadow-xl outline-1"
                id="editor-container"
                style={{ height: "60vh" }}
              >
                <CodeMirror
                  onChange={(value) => {
                    try {
                      if (!isInterviewActive) {
                        return;
                      }
                      if (value && !currentQuestion?.startTimestamp) {
                        startAnswering();
                      }
                      if (typeof updateAnswer !== 'function') {
                        console.error('updateAnswer is not a function:', updateAnswer);
                        return;
                      }
                      updateAnswer(value);
                    } catch (error) {
                      console.error('Error updating answer:', error);
                    }
                  }}
                  value={currentQuestion?.answer || ''}
                  theme="dark"
                  height="100%"
                  readOnly={!isInterviewActive}
                />
              </div>
            </div>
        
        )}
      </div>

      <div className="flex flex-row items-center justify-center space-x-4 py-2">
        <div className="flex space-x-4">
          <button
            className={`flex items-center rounded-3xl border-2 px-4 py-2 font-bold transition ${
              isProcessing || isSpeaking || !isInterviewActive
                ? "cursor-not-allowed border-gray-400 bg-gray-400"
                : "border-[#6b88ff] bg-[#6b88ff] hover:scale-105"
            } text-white`}
            onClick={async () => {
              try {
                if (!isInterviewActive) {
                  return;
                }
                // Save current answer before moving to next question
                const currentAnswer = currentQuestion?.answer;
                if (currentAnswer !== undefined) {
                  await new Promise(resolve => setTimeout(resolve, 200));
                  await updateAnswer(currentAnswer);
                  // Wait for state update

                }

                const success = await handleNextQuestion();
                if (!success) {
                  return;
                }
              } catch (error) {
                console.error("Error moving to next question:", error);
              }
            }}
            disabled={isProcessing || isSpeaking || !isInterviewActive}
          >
            {isProcessing ? (
              <div className="flex items-center">
                <svg
                  className="mr-2 h-4 w-4 animate-spin text-white"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                <span>Generating next question...</span>
              </div>
            ) : (
              <>
                <span className="font-bold text-white">Next Question</span>
                <ArrowRight className="ml-2 h-5 w-5" />
              </>
            )}
          </button>

          <button
            className={`flex items-center rounded-3xl border-2 px-4 py-2 font-bold transition ${
              currentIndex + 1 < 5 || !isInterviewActive
                ? "cursor-not-allowed border-gray-300 text-gray-300"
                : "border-gray-600 text-gray-600 hover:scale-105"
            }`}
              onClick={(e) => {
              e.preventDefault();
              console.log("🏁 Finish Interview button clicked", {
                currentIndex,
                questionsCompleted: currentIndex + 1,
                isEnabled: currentIndex + 1 >= 5 && isInterviewActive,
                isInterviewActive
              });
              if (currentIndex + 1 >= 5) {
                const button = e.currentTarget;
                // Disable button and update UI
                button.disabled = true;
                button.classList.add('opacity-50', 'cursor-not-allowed');
                button.innerHTML = `
                  <div class="flex items-center">
                    <svg class="animate-spin h-5 w-5 mr-3 text-gray-600" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="font-bold text-gray-600">Processing interview...</span>
                  </div>`;
                onEndInterview();
              }
            }}
            disabled={currentIndex + 1 < 5 || !isInterviewActive}
            title={
                currentIndex + 1 < 5
                ? "Please complete at least 5 questions"
                : "Click to finish the interview"
            }
          >
            <span
              className={`font-bold ${
                currentIndex + 1 < 5 ? "text-gray-300" : "text-green-600"
              }`}
            >
              Finish Interview{" "}
              {currentIndex + 1 < 5
                ? `(${5 - (currentIndex + 1)} more required)`
                : "✓"}
            </span>
            <LogOut className="ml-2 h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  );
}
