import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import Cookies from "js-cookie";
import Loader from 'components/Loader';
import ReviewMockInterview from './components/ReviewMockInterview';
import toast from 'react-hot-toast';

const MockInterviewPreview = () => {
  const { id } = useParams();
  const candId = Cookies.get("candId");
  const [isLoading, setIsLoading] = useState(true);
  const [interviewData, setInterviewData] = useState(null);

  useEffect(() => {
    const fetchMockInterview = async () => {
      try {
        setIsLoading(true);
        console.log('Fetching for mock_interview_id:', id);
        
        // First get the video profile data
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/mock-resume/${candId}`,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );

        if (!response.ok) {
          throw new Error('Failed to fetch mock interview data');
        }

        const profiles = await response.json();
        console.log('All profiles:', profiles);
        console.log('Looking for profile with mock_interview_id:', id);
        
        const currentProfile = profiles.find(p => {
          console.log('Checking profile:', p.mock_interview_id, typeof p.mock_interview_id);
          return String(p.mock_interview_id) === String(id);
        });
        
        if (!currentProfile) {
          console.log('No matching profile found');
          throw new Error('Profile not found');
        }

        console.log('Found profile:', currentProfile);

        // Parse the score JSON string from the profile
        let scoreData = {};
        if (currentProfile.score) {
          try {
            scoreData = JSON.parse(currentProfile.score);
            console.log('Parsed score data:', scoreData);
          } catch (e) {
            console.error('Error parsing score JSON:', e);
            throw new Error('Invalid score data');
          }
        }

        setInterviewData({
          videoUrl: currentProfile.video_url,
          score: scoreData,
          status: currentProfile.status
        });

      } catch (error) {
        console.error('Error fetching mock interview:', error);
        toast.error(error.message || 'Failed to load mock interview details');
      } finally {
        setIsLoading(false);
      }
    };

    if (id && candId) {
      fetchMockInterview();
    }
  }, [id, candId]);

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader text="Loading mock interview..." />
      </div>
    );
  }

  if (!interviewData || !interviewData.videoUrl) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-800">Mock Interview Not Found</h2>
          <p className="mt-2 text-gray-600">
            The requested mock interview could not be loaded.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <ReviewMockInterview
        videoUrl={interviewData.videoUrl}
        score={interviewData.score}
        status={interviewData.status}
      />
    </div>
  );
};

export default MockInterviewPreview;